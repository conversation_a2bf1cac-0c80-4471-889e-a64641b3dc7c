# Desktop Clock (Electron)

A professional, beautiful desktop clock app for Ubuntu, built with Electron.

## Features
- Modern, stylish digital clock UI
- Clickable desktop app with .desktop launcher
- Resizable window
- Custom icon

## Setup
1. Install Node.js (https://nodejs.org/)
2. In this directory, run:
   npm install
3. To start the clock:
   npm start

## Add to Desktop
- Copy `DesktopClock.desktop` to your desktop or `~/.local/share/applications/` for easy launching.
- Make sure the .desktop file is executable:
  chmod +x DesktopClock.desktop

## Customization
- Edit `style.css` for appearance tweaks.

---

© 2025
