// Weather utility for fetching NWS weather given a lat/lon
export async function getNWSWeather(lat, lon) {
  // NWS API endpoint for points
  const pointUrl = `https://api.weather.gov/points/${lat},${lon}`;
  const pointResp = await fetch(pointUrl);
  if (!pointResp.ok) throw new Error('Could not fetch NWS point data');
  const pointData = await pointResp.json();
  const forecastUrl = pointData.properties.forecast;
  const forecastResp = await fetch(forecastUrl);
  if (!forecastResp.ok) throw new Error('Could not fetch NWS forecast');
  const forecastData = await forecastResp.json();
  // Return the first period (current/next period)
  return forecastData.properties.periods[0];
}
