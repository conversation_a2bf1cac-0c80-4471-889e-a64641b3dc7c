const { app, BrowserWindow, session } = require('electron');
const path = require('path');

function createWindow () {
  const win = new BrowserWindow({
    width: 500,
    height: 350,
    minWidth: 400,
    minHeight: 300,
    resizable: true,
    alwaysOnTop: false,
    icon: path.join(__dirname, 'assets/clock.png'),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    title: "Desktop Clock"
  });
  win.setMenuBarVisibility(false);
  win.loadFile('index.html');
  // Remove scrollbars
  win.webContents.on('did-finish-load', () => {
    win.webContents.insertCSS('body { overflow: hidden !important; } html { overflow: hidden !important; }');
  });
}

app.whenReady().then(() => {
  // Allow geolocation permission requests
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    if (permission === 'geolocation') {
      callback(true);
    } else {
      callback(false);
    }
  });
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
