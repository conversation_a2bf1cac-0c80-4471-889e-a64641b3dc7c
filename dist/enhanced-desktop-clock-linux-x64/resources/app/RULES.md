# Rules for DesktopClock Project

1. After making any code changes, always re-run electron-packager to update the standalone app.
2. Always ensure the weather section is visible and below the clock digits.
3. Use NWS API with hardcoded or user-supplied lat/lon for weather (no API key needed).
4. Keep the UI large enough to display all information without scrollbars.
5. Update the .desktop launcher if the packaged app location or name changes.
