body {
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Segoe UI', '<PERSON><PERSON>', Arial, sans-serif;
}
.clock-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}
.clock {
  color: #ffffff;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 36px 48px;
  font-size: 3.2em;
  font-weight: 700;
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.3), 0 0 20px rgba(100, 200, 255, 0.1);
  text-align: center;
  letter-spacing: 0.08em;
  min-width: 320px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.date-info {
  color: #e8f4fd;
  font-size: 1.4em;
  margin-top: 16px;
  text-shadow: 0 2px 12px rgba(0,0,0,0.7);
  font-weight: 500;
  letter-spacing: 0.04em;
  text-align: center;
  opacity: 0.95;
}

.weather {
  color: #ffffff;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  margin-top: 28px;
  font-size: 1.1em;
  font-weight: 400;
  padding: 20px 28px;
  text-align: left;
  box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.2), 0 0 15px rgba(100, 200, 255, 0.05);
  max-width: 420px;
  min-width: 380px;
}

#weather-label {
  display: block;
  font-size: 1.1em;
  font-weight: 600;
  margin-bottom: 16px;
  color: #64b5f6;
  text-align: center;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
}

.weather-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.current-temp {
  font-size: 2.2em;
  font-weight: 700;
  color: #4fc3f7;
  text-shadow: 0 2px 8px rgba(79, 195, 247, 0.3);
}

.condition {
  font-size: 1.2em;
  font-weight: 500;
  color: #e1f5fe;
  text-align: right;
  flex: 1;
  margin-left: 20px;
}

.weather-details {
  display: grid;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 600;
  color: #90caf9;
  font-size: 0.95em;
  min-width: 80px;
}

.detail-value {
  font-weight: 500;
  color: #ffffff;
  text-align: right;
  font-size: 0.95em;
}
