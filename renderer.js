async function getNWSWeatherAndTemp(lat, lon) {
  lat = parseFloat(lat).toFixed(4);
  lon = parseFloat(lon).toFixed(4);
  function fetchWithUA(url) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url);
      xhr.setRequestHeader('User-Agent', 'DesktopClockApp (nichols-ai@localhost)');
      xhr.setRequestHeader('Accept', 'application/geo+json,application/json;q=0.9');
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(JSON.parse(xhr.responseText));
        } else {
          reject(new Error('Request failed: ' + xhr.status));
        }
      };
      xhr.onerror = () => reject(new Error('Network error'));
      xhr.send();
    });
  }
  // NWS API endpoint for points
  const pointUrl = `https://api.weather.gov/points/${lat},${lon}`;
  const pointData = await fetchWithUA(pointUrl);
  const forecastUrl = pointData.properties.forecast;
  const observationStationsUrl = pointData.properties.observationStations;

  // Get forecast (first period)
  const forecastData = await fetchWithUA(forecastUrl);
  const forecast = forecastData.properties.periods[0];

  // Get nearest observation station
  const stationsData = await fetchWithUA(observationStationsUrl);
  const stationId = stationsData.features[0]?.properties.stationIdentifier;
  let currentTemp = null;
  let currentTempUnit = null;
  let obsData = null; // Declare obsData at function level
  
  if (stationId) {
    const observationsUrl = `https://api.weather.gov/stations/${stationId}/observations/latest`;
    try {
      obsData = await fetchWithUA(observationsUrl);
      // Debug: log the full observation response
      console.log('NWS Observation response:', obsData);
      currentTemp = obsData.properties.temperature.value;
      const unitCode = obsData.properties.temperature.unitCode;
      if (currentTemp === null || isNaN(currentTemp)) {
        currentTemp = null;
        currentTempUnit = '';
      } else if (unitCode === 'unit:degC' || unitCode === 'wmoUnit:degC') {
        // Convert Celsius to Fahrenheit
        currentTemp = Math.round((currentTemp * 9/5) + 32);
        currentTempUnit = '°F';
      } else if (unitCode === 'unit:degF' || unitCode === 'wmoUnit:degF') {
        currentTemp = Math.round(currentTemp);
        currentTempUnit = '°F';
      } else if (unitCode === 'unit:K' || unitCode === 'wmoUnit:K') {
        // Convert Kelvin to Fahrenheit
        currentTemp = Math.round((currentTemp - 273.15) * 9/5 + 32);
        currentTempUnit = '°F';
      } else {
        // Unknown unit, show raw value and unit code
        currentTemp = Math.round(currentTemp);
        currentTempUnit = unitCode ? ` (${unitCode})` : '';
      }

    } catch (e) {
      // If observation fails, just skip currentTemp
      currentTemp = null;
      obsData = null;
    }
  }
  return { forecast, currentTemp, currentTempUnit, obsData };
}

function updateClock() {
  const clock = document.getElementById('clock');
  const dateInfo = document.getElementById('date-info');
  const now = new Date();
  let hours = now.getHours();
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const ampm = hours >= 12 ? 'PM' : 'AM';
  
  // Convert to 12-hour format
  hours = hours % 12;
  hours = hours ? hours : 12; // the hour '0' should be '12'
  const displayHours = String(hours).padStart(2, '0');
  
  clock.textContent = `${displayHours}:${minutes}:${seconds} ${ampm}`;

  // Date and day of week
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  const dayOfWeek = days[now.getDay()];
  const month = months[now.getMonth()];
  const day = now.getDate();
  const year = now.getFullYear();
  const dateStr = `${dayOfWeek}, ${month} ${day}, ${year}`;
  if (dateInfo) {
    dateInfo.textContent = dateStr;
  }
}

function setWeatherText(text) {
  const weatherInfo = document.getElementById('weather-info');
  if (weatherInfo) {
    weatherInfo.textContent = text;
  }
}

function setWeatherHTML(html) {
  const weatherInfo = document.getElementById('weather-info');
  if (weatherInfo) {
    weatherInfo.innerHTML = html;
  }
}

async function fetchAndShowWeather() {
  // Enterprise, Alabama, USA coordinates
  const latitude = 31.3152;
  const longitude = -85.8552;
  setWeatherText('Loading weather...');
  try {
    setWeatherText('Fetching weather...');
    const { forecast, currentTemp, currentTempUnit, obsData } = await getNWSWeatherAndTemp(latitude, longitude);
    
    // Extract additional weather details
    let humidity = 'N/A';
    let windSpeed = 'N/A';
    let windDirection = 'N/A';
    let pressure = 'N/A';
    let visibility = 'N/A';
    
    if (obsData && obsData.properties) {
      const props = obsData.properties;
      
      if (props.relativeHumidity && props.relativeHumidity.value !== null) {
        humidity = Math.round(props.relativeHumidity.value) + '%';
      }
      
      if (props.windSpeed && props.windSpeed.value !== null) {
        const windSpeedMph = Math.round(props.windSpeed.value * 2.237); // Convert m/s to mph
        windSpeed = windSpeedMph + ' mph';
      }
      
      if (props.windDirection && props.windDirection.value !== null) {
        const degrees = props.windDirection.value;
        const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
        const index = Math.round(degrees / 22.5) % 16;
        windDirection = directions[index];
      }
      
      if (props.barometricPressure && props.barometricPressure.value !== null) {
        const pressureInHg = (props.barometricPressure.value / 3386.39).toFixed(2); // Convert Pa to inHg
        pressure = pressureInHg + ' inHg';
      }
      
      if (props.visibility && props.visibility.value !== null) {
        const visibilityMiles = (props.visibility.value / 1609.34).toFixed(1); // Convert m to miles
        visibility = visibilityMiles + ' mi';
      }
    }
    
    // Create enhanced weather display
    const currentTempDisplay = currentTemp !== null ? `${currentTemp}${currentTempUnit}` : 'N/A';
    const forecastTempDisplay = `${forecast.temperature}°${forecast.temperatureUnit}`;
    
    const weatherHTML = `
      <div class="weather-main">
        <div class="current-temp">${currentTempDisplay}</div>
        <div class="condition">${forecast.shortForecast}</div>
      </div>
      <div class="weather-details">
        <div class="detail-row">
          <span class="detail-label">Forecast:</span>
          <span class="detail-value">${forecastTempDisplay} - ${forecast.name}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Humidity:</span>
          <span class="detail-value">${humidity}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Wind:</span>
          <span class="detail-value">${windSpeed} ${windDirection}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Pressure:</span>
          <span class="detail-value">${pressure}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Visibility:</span>
          <span class="detail-value">${visibility}</span>
        </div>
      </div>
    `;
    
    setWeatherHTML(weatherHTML);
  } catch (e) {
    setWeatherText('Weather unavailable: ' + (e && e.message ? e.message : e));
  }
}

setInterval(updateClock, 1000);
updateClock();
fetchAndShowWeather();

// Refresh weather every 10 minutes (600,000 ms)
setInterval(fetchAndShowWeather, 600000);

// Allow manual refresh by clicking the weather area
const weatherDiv = document.getElementById('weather');
if (weatherDiv) {
  weatherDiv.style.cursor = 'pointer';
  weatherDiv.title = 'Click to refresh weather';
  weatherDiv.addEventListener('click', () => {
    setWeatherText('Refreshing...');
    fetchAndShowWeather();
  });
}
